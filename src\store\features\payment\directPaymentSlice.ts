import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { AxiosError } from "axios";
import { axiosInstance } from "@/store/axiosInstance";

// Interface for API payload
export interface DirectPaymentApiData {
  expiryMonth: string;
  expiryYear: string;
  amount: string;
  orderId: string;
  userName: string;
  userEmail: string;
  userPhone: string;
  cardNumber: string;
  cvv: string;
  nameOnCard: string;
  country: string;
  whereDidYouHear?: string;
  companyName: string;
  taxId: string;
  taxOffice?: string;
  address: string;
  email: string;
  phoneNumber: string;
}

export interface DirectPaymentResult {
  success: boolean;
  message: string;
  transactionId?: string;
  amount?: string;
  errorCode?: string;
  html?: string; // For 3D Secure HTML response
}

export interface DirectPaymentState {
  loading: boolean;
  error: string | null;
  result: DirectPaymentResult | null;
  show3DModal: boolean;
  iframeHtml: string;
  paymentStatus: "pending" | "success" | "fail";
}

const initialState: DirectPaymentState = {
  loading: false,
  error: null,
  result: null,
  show3DModal: false,
  iframeHtml: "",
  paymentStatus: "pending",
};

// Async thunk for direct payment processing
export const processDirectPayment = createAsyncThunk(
  "directPayment/processDirectPayment",
  async (paymentData: DirectPaymentApiData, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/api/paytr/direct-payment", paymentData);

      // Check if response is HTML (3D Secure)
      const contentType = response.headers['content-type'];
      if (contentType?.includes("text/html")) {
        let html = response.data;

        // Modify HTML for iframe compatibility
        html = html
          .replace("</body>", `
            <script>
              document.querySelector('input[type="button"][value="Cancel"]')?.addEventListener('click', function(e) {
                window.parent.postMessage({ type: 'CANCEL_3D' }, '*');
                e.preventDefault();
              });
            </script>
          </body>`)
          .replace(/target=("|')?_top("|')?/gi, 'target="_self"');

        return {
          success: true,
          message: "3D Secure verification required",
          html: html,
        };
      }

      // Handle JSON response
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      return rejectWithValue(
        axiosError.response?.data || 'Failed to process payment'
      );
    }
  }
);

const directPaymentSlice = createSlice({
  name: "directPayment",
  initialState,
  reducers: {
    clearPaymentResult: (state) => {
      state.result = null;
      state.error = null;
    },
    setShow3DModal: (state, action: PayloadAction<boolean>) => {
      state.show3DModal = action.payload;
    },
    setIframeHtml: (state, action: PayloadAction<string>) => {
      state.iframeHtml = action.payload;
    },
    setPaymentStatus: (state, action: PayloadAction<"pending" | "success" | "fail">) => {
      state.paymentStatus = action.payload;
    },
    resetPaymentState: (state) => {
      state.loading = false;
      state.error = null;
      state.result = null;
      state.show3DModal = false;
      state.iframeHtml = "";
      state.paymentStatus = "pending";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(processDirectPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.result = null;
      })
      .addCase(processDirectPayment.fulfilled, (state, action: PayloadAction<DirectPaymentResult>) => {
        state.loading = false;
        state.result = action.payload;
        
        // Handle 3D Secure response
        if (action.payload.html) {
          state.iframeHtml = action.payload.html;
          state.show3DModal = true;
        }
      })
      .addCase(processDirectPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.result = {
          success: false,
          message: action.payload as string,
        };
      });
  },
});

export const {
  clearPaymentResult,
  setShow3DModal,
  setIframeHtml,
  setPaymentStatus,
  resetPaymentState,
} = directPaymentSlice.actions;

export default directPaymentSlice.reducer;
